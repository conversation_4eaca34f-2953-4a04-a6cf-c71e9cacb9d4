D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f CMakeFiles\test_server.dir/objects.a
D:\AAVM\software\mingw64\bin\ar.exe qc CMakeFiles\test_server.dir/objects.a @CMakeFiles\test_server.dir\objects1.rsp
D:\AAVM\software\mingw64\bin\g++.exe  -Wall -Wextra -g -Wl,--whole-archive CMakeFiles\test_server.dir/objects.a -Wl,--no-whole-archive -o bin\test_server.exe -Wl,--out-implib,libtest_server.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\test_server.dir\linkLibs.rsp
