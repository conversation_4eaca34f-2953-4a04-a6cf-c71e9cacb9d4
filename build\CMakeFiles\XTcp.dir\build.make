# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\C++source\Tools\Cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\AAVM\Http_Server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\AAVM\Http_Server\build

# Include any dependencies generated for this target.
include CMakeFiles/XTcp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/XTcp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/XTcp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/XTcp.dir/flags.make

CMakeFiles/XTcp.dir/codegen:
.PHONY : CMakeFiles/XTcp.dir/codegen

CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj: CMakeFiles/XTcp.dir/flags.make
CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj: CMakeFiles/XTcp.dir/includes_CXX.rsp
CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj: D:/AAVM/Http_Server/Test/XTcp.cpp
CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj: CMakeFiles/XTcp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj -MF CMakeFiles\XTcp.dir\Test\XTcp.cpp.obj.d -o CMakeFiles\XTcp.dir\Test\XTcp.cpp.obj -c D:\AAVM\Http_Server\Test\XTcp.cpp

CMakeFiles/XTcp.dir/Test/XTcp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/XTcp.dir/Test/XTcp.cpp.i"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\AAVM\Http_Server\Test\XTcp.cpp > CMakeFiles\XTcp.dir\Test\XTcp.cpp.i

CMakeFiles/XTcp.dir/Test/XTcp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/XTcp.dir/Test/XTcp.cpp.s"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\AAVM\Http_Server\Test\XTcp.cpp -o CMakeFiles\XTcp.dir\Test\XTcp.cpp.s

# Object files for target XTcp
XTcp_OBJECTS = \
"CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj"

# External object files for target XTcp
XTcp_EXTERNAL_OBJECTS =

bin/libXTcp.dll: CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj
bin/libXTcp.dll: CMakeFiles/XTcp.dir/build.make
bin/libXTcp.dll: CMakeFiles/XTcp.dir/linkLibs.rsp
bin/libXTcp.dll: CMakeFiles/XTcp.dir/objects1.rsp
bin/libXTcp.dll: CMakeFiles/XTcp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library bin\libXTcp.dll"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\XTcp.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/XTcp.dir/build: bin/libXTcp.dll
.PHONY : CMakeFiles/XTcp.dir/build

CMakeFiles/XTcp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\XTcp.dir\cmake_clean.cmake
.PHONY : CMakeFiles/XTcp.dir/clean

CMakeFiles/XTcp.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\AAVM\Http_Server D:\AAVM\Http_Server D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build\CMakeFiles\XTcp.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/XTcp.dir/depend

