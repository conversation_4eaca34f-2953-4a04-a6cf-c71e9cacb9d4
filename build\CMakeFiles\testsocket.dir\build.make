# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\C++source\Tools\Cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\AAVM\Http_Server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\AAVM\Http_Server\build

# Include any dependencies generated for this target.
include CMakeFiles/testsocket.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/testsocket.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/testsocket.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/testsocket.dir/flags.make

CMakeFiles/testsocket.dir/codegen:
.PHONY : CMakeFiles/testsocket.dir/codegen

CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj: CMakeFiles/testsocket.dir/flags.make
CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj: CMakeFiles/testsocket.dir/includes_CXX.rsp
CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj: D:/AAVM/Http_Server/Test/testsocket.cpp
CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj: CMakeFiles/testsocket.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj -MF CMakeFiles\testsocket.dir\Test\testsocket.cpp.obj.d -o CMakeFiles\testsocket.dir\Test\testsocket.cpp.obj -c D:\AAVM\Http_Server\Test\testsocket.cpp

CMakeFiles/testsocket.dir/Test/testsocket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/testsocket.dir/Test/testsocket.cpp.i"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\AAVM\Http_Server\Test\testsocket.cpp > CMakeFiles\testsocket.dir\Test\testsocket.cpp.i

CMakeFiles/testsocket.dir/Test/testsocket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/testsocket.dir/Test/testsocket.cpp.s"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\AAVM\Http_Server\Test\testsocket.cpp -o CMakeFiles\testsocket.dir\Test\testsocket.cpp.s

# Object files for target testsocket
testsocket_OBJECTS = \
"CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj"

# External object files for target testsocket
testsocket_EXTERNAL_OBJECTS =

bin/testsocket.exe: CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj
bin/testsocket.exe: CMakeFiles/testsocket.dir/build.make
bin/testsocket.exe: lib/libXTcp.dll.a
bin/testsocket.exe: CMakeFiles/testsocket.dir/linkLibs.rsp
bin/testsocket.exe: CMakeFiles/testsocket.dir/objects1.rsp
bin/testsocket.exe: CMakeFiles/testsocket.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin\testsocket.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\testsocket.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/testsocket.dir/build: bin/testsocket.exe
.PHONY : CMakeFiles/testsocket.dir/build

CMakeFiles/testsocket.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\testsocket.dir\cmake_clean.cmake
.PHONY : CMakeFiles/testsocket.dir/clean

CMakeFiles/testsocket.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\AAVM\Http_Server D:\AAVM\Http_Server D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build\CMakeFiles\testsocket.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/testsocket.dir/depend

