D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f CMakeFiles\fixed_client.dir/objects.a
D:\AAVM\software\mingw64\bin\ar.exe qc CMakeFiles\fixed_client.dir/objects.a @CMakeFiles\fixed_client.dir\objects1.rsp
D:\AAVM\software\mingw64\bin\g++.exe  -Wall -Wextra -g -Wl,--whole-archive CMakeFiles\fixed_client.dir/objects.a -Wl,--no-whole-archive -o bin\fixed_client.exe -Wl,--out-implib,libfixed_client.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\fixed_client.dir\linkLibs.rsp
