# XTcp DLL 使用说明

## 构建DLL

1. 在项目根目录执行以下命令：
```bash
mkdir build
cd build
cmake ..
make
```

2. 构建完成后，你会在以下位置找到文件：
   - `build/bin/XTcp.dll` - 动态库文件
   - `build/lib/libXTcp.dll.a` - 导入库文件（MinGW）
   - `Test/XTcp.h` - 头文件

## 在客户端项目中使用

### 1. 复制必要文件
将以下文件复制到你的客户端项目：
- `XTcp.dll` - 放在可执行文件同目录或系统PATH中
- `XTcp.h` - 放在你的项目include目录
- `libXTcp.dll.a` - 用于链接（MinGW）

### 2. 编译设置
在你的CMakeLists.txt中添加：
```cmake
# 包含头文件目录
include_directories(path/to/XTcp/header)

# 链接XTcp库
target_link_libraries(your_target XTcp)
if(WIN32)
    target_link_libraries(your_target ws2_32)
endif()
```

或者使用g++直接编译：
```bash
g++ -o client client.cpp -L. -lXTcp -lws2_32
```

### 3. 代码示例
```cpp
#include "XTcp.h"
#include <iostream>

int main() {
    XTcp client;
    
    // 创建socket
    if (client.CreateSocket() <= 0) {
        std::cout << "创建socket失败" << std::endl;
        return -1;
    }
    
    // 使用socket进行网络通信
    // ...
    
    client.Close();
    return 0;
}
```

## API 说明

### XTcp 类方法：
- `int CreateSocket()` - 创建socket，返回socket描述符
- `bool Bind(unsigned short port)` - 绑定端口（服务器端使用）
- `XTcp Accept()` - 接受客户端连接（服务器端使用）
- `void Close()` - 关闭socket连接
- `int Recv(char *buf, int bufsize)` - 接收数据
- `int Send(const char *buf, int sendsize)` - 发送数据

### 注意事项：
1. 在Windows上使用时，确保链接ws2_32库
2. DLL文件必须在可执行文件能找到的路径中
3. 使用前需要调用CreateSocket()创建socket
4. 使用完毕后调用Close()释放资源
