D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f CMakeFiles\XTcp.dir/objects.a
D:\AAVM\software\mingw64\bin\ar.exe qc CMakeFiles\XTcp.dir/objects.a @CMakeFiles\XTcp.dir\objects1.rsp
D:\AAVM\software\mingw64\bin\g++.exe  -Wall -Wextra -g -shared -o bin\libXTcp.dll -Wl,--out-implib,lib\libXTcp.dll.a -Wl,--major-image-version,0,--minor-image-version,0 -Wl,--whole-archive CMakeFiles\XTcp.dir/objects.a -Wl,--no-whole-archive @CMakeFiles\XTcp.dir\linkLibs.rsp
