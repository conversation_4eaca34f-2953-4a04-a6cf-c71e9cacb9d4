# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\C++source\Tools\Cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\AAVM\Http_Server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\AAVM\Http_Server\build

# Include any dependencies generated for this target.
include CMakeFiles/test_server.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_server.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_server.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_server.dir/flags.make

CMakeFiles/test_server.dir/codegen:
.PHONY : CMakeFiles/test_server.dir/codegen

CMakeFiles/test_server.dir/test_server.cpp.obj: CMakeFiles/test_server.dir/flags.make
CMakeFiles/test_server.dir/test_server.cpp.obj: CMakeFiles/test_server.dir/includes_CXX.rsp
CMakeFiles/test_server.dir/test_server.cpp.obj: D:/AAVM/Http_Server/test_server.cpp
CMakeFiles/test_server.dir/test_server.cpp.obj: CMakeFiles/test_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_server.dir/test_server.cpp.obj"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_server.dir/test_server.cpp.obj -MF CMakeFiles\test_server.dir\test_server.cpp.obj.d -o CMakeFiles\test_server.dir\test_server.cpp.obj -c D:\AAVM\Http_Server\test_server.cpp

CMakeFiles/test_server.dir/test_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_server.dir/test_server.cpp.i"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\AAVM\Http_Server\test_server.cpp > CMakeFiles\test_server.dir\test_server.cpp.i

CMakeFiles/test_server.dir/test_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_server.dir/test_server.cpp.s"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\AAVM\Http_Server\test_server.cpp -o CMakeFiles\test_server.dir\test_server.cpp.s

# Object files for target test_server
test_server_OBJECTS = \
"CMakeFiles/test_server.dir/test_server.cpp.obj"

# External object files for target test_server
test_server_EXTERNAL_OBJECTS =

bin/test_server.exe: CMakeFiles/test_server.dir/test_server.cpp.obj
bin/test_server.exe: CMakeFiles/test_server.dir/build.make
bin/test_server.exe: lib/libXTcp.dll.a
bin/test_server.exe: CMakeFiles/test_server.dir/linkLibs.rsp
bin/test_server.exe: CMakeFiles/test_server.dir/objects1.rsp
bin/test_server.exe: CMakeFiles/test_server.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin\test_server.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\test_server.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_server.dir/build: bin/test_server.exe
.PHONY : CMakeFiles/test_server.dir/build

CMakeFiles/test_server.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\test_server.dir\cmake_clean.cmake
.PHONY : CMakeFiles/test_server.dir/clean

CMakeFiles/test_server.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\AAVM\Http_Server D:\AAVM\Http_Server D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build\CMakeFiles\test_server.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_server.dir/depend

