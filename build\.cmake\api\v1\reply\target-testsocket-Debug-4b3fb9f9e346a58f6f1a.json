{"artifacts": [{"path": "bin/testsocket.exe"}, {"path": "bin/testsocket.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "set_property", "find_package", "add_definitions", "include_directories"], "files": ["CMakeLists.txt", "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/FindThreads.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 47, "parent": 0}, {"command": 1, "file": 0, "line": 70, "parent": 0}, {"command": 2, "file": 0, "line": 52, "parent": 0}, {"command": 2, "file": 0, "line": 54, "parent": 0}, {"command": 4, "file": 0, "line": 26, "parent": 0}, {"file": 1, "parent": 5}, {"command": 3, "file": 1, "line": 238, "parent": 6}, {"command": 5, "file": 0, "line": 13, "parent": 0}, {"command": 6, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -std=gnu++11"}], "defines": [{"backtrace": 8, "define": "WIN32"}], "includes": [{"backtrace": 9, "path": "D:/AAVM/Http_Server/Test"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "XTcp::@6890427a1f51a3e7e1df"}], "id": "testsocket::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/Project"}}, "link": {"commandFragments": [{"fragment": "-Wall -Wextra -g", "role": "flags"}, {"backtrace": 3, "fragment": "lib\\libXTcp.dll.a", "role": "libraries"}, {"backtrace": 4, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 7, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "testsocket", "nameOnDisk": "testsocket.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Test/testsocket.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}