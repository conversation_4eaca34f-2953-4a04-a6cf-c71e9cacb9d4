{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-c97369ccbd9a69944e82.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Http_Server", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "XTcp::@6890427a1f51a3e7e1df", "jsonFile": "target-XTcp-Debug-8e2ab8162b6618fd0dd5.json", "name": "XTcp", "projectIndex": 0}, {"directoryIndex": 0, "id": "testsocket::@6890427a1f51a3e7e1df", "jsonFile": "target-testsocket-Debug-4b3fb9f9e346a58f6f1a.json", "name": "testsocket", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AAVM/Http_Server/build", "source": "D:/AAVM/Http_Server"}, "version": {"major": 2, "minor": 8}}