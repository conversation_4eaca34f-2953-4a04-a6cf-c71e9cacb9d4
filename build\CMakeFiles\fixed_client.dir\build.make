# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\C++source\Tools\Cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\AAVM\Http_Server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\AAVM\Http_Server\build

# Include any dependencies generated for this target.
include CMakeFiles/fixed_client.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/fixed_client.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/fixed_client.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/fixed_client.dir/flags.make

CMakeFiles/fixed_client.dir/codegen:
.PHONY : CMakeFiles/fixed_client.dir/codegen

CMakeFiles/fixed_client.dir/fixed_client.cpp.obj: CMakeFiles/fixed_client.dir/flags.make
CMakeFiles/fixed_client.dir/fixed_client.cpp.obj: CMakeFiles/fixed_client.dir/includes_CXX.rsp
CMakeFiles/fixed_client.dir/fixed_client.cpp.obj: D:/AAVM/Http_Server/fixed_client.cpp
CMakeFiles/fixed_client.dir/fixed_client.cpp.obj: CMakeFiles/fixed_client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/fixed_client.dir/fixed_client.cpp.obj"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/fixed_client.dir/fixed_client.cpp.obj -MF CMakeFiles\fixed_client.dir\fixed_client.cpp.obj.d -o CMakeFiles\fixed_client.dir\fixed_client.cpp.obj -c D:\AAVM\Http_Server\fixed_client.cpp

CMakeFiles/fixed_client.dir/fixed_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/fixed_client.dir/fixed_client.cpp.i"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\AAVM\Http_Server\fixed_client.cpp > CMakeFiles\fixed_client.dir\fixed_client.cpp.i

CMakeFiles/fixed_client.dir/fixed_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/fixed_client.dir/fixed_client.cpp.s"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\AAVM\Http_Server\fixed_client.cpp -o CMakeFiles\fixed_client.dir\fixed_client.cpp.s

# Object files for target fixed_client
fixed_client_OBJECTS = \
"CMakeFiles/fixed_client.dir/fixed_client.cpp.obj"

# External object files for target fixed_client
fixed_client_EXTERNAL_OBJECTS =

bin/fixed_client.exe: CMakeFiles/fixed_client.dir/fixed_client.cpp.obj
bin/fixed_client.exe: CMakeFiles/fixed_client.dir/build.make
bin/fixed_client.exe: lib/libXTcp.dll.a
bin/fixed_client.exe: CMakeFiles/fixed_client.dir/linkLibs.rsp
bin/fixed_client.exe: CMakeFiles/fixed_client.dir/objects1.rsp
bin/fixed_client.exe: CMakeFiles/fixed_client.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin\fixed_client.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\fixed_client.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/fixed_client.dir/build: bin/fixed_client.exe
.PHONY : CMakeFiles/fixed_client.dir/build

CMakeFiles/fixed_client.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\fixed_client.dir\cmake_clean.cmake
.PHONY : CMakeFiles/fixed_client.dir/clean

CMakeFiles/fixed_client.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\AAVM\Http_Server D:\AAVM\Http_Server D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build D:\AAVM\Http_Server\build\CMakeFiles\fixed_client.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/fixed_client.dir/depend

