# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "D:/AAVM/Http_Server/CMakeLists.txt"
  "CMakeFiles/4.1.0/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeRCInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CheckIncludeFile.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CheckLibraryExists.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-C.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/FindPackageHandleStandardArgs.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/FindPackageMessage.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/FindThreads.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCLinkerInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-C.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-windres.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/XTcp.dir/DependInfo.cmake"
  "CMakeFiles/testsocket.dir/DependInfo.cmake"
  )
